import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:image/image.dart' as img;
import 'package:image_compressor/image_compressor.dart';
import 'package:shared/shared.dart';

/// A singleton handler for compressing images with concurrency control.
class ImageCompressHandler {
  ImageCompressHandler._internal();

  static final ImageCompressHandler _instance =
      ImageCompressHandler._internal();

  factory ImageCompressHandler() => _instance;

  static const int MIN_SIZE_KB = 128;
  static const int MAX_SIZE_THUMBNAIL = 512;
  static const int MAX_FILE_SIZE_THUMBNAIL_KB = 200;

  final int defaultCompressQuality = 80;
  final int maxCompressQuality = 100;

  // Max concurrent compression tasks
  final int _maxConcurrentTasks = GlobalConfig.maxConcurrentCompressImageTasks;
  final List<Completer<void>> _taskQueue = [];
  int _currentRunning = 0;

  // Concurrency control wrapper
  Future<T> _runWithLimit<T>(Future<T> Function() task) async {
    if (_currentRunning >= _maxConcurrentTasks) {
      final completer = Completer<void>();
      _taskQueue.add(completer);
      await completer.future;
    }

    _currentRunning++;
    try {
      return await task();
    } finally {
      _currentRunning--;
      if (_taskQueue.isNotEmpty) {
        _taskQueue.removeAt(0).complete();
      }
    }
  }

  Future<Size> getImageDimensionsFormFile(File image) async {
    final completer = Completer<Size>();
    FileImage(image).resolve(const ImageConfiguration()).addListener(
      ImageStreamListener((info, _) {
        if (!completer.isCompleted) {
          completer.complete(
              Size(info.image.width.toDouble(), info.image.height.toDouble()));
        }
      }),
    );
    return completer.future;
  }

  Future<Size> getImageDimensionsFormData(Uint8List imageData) async {
    final completer = Completer<Size>();
    MemoryImage(imageData).resolve(const ImageConfiguration()).addListener(
      ImageStreamListener((info, _) {
        if (!completer.isCompleted) {
          completer.complete(
              Size(info.image.width.toDouble(), info.image.height.toDouble()));
        }
      }),
    );
    return completer.future;
  }

  Size _getOriginalSize(double sourceWidth, double sourceHeight) {
    const maxWidth = 1080;
    const maxHeight = 1920;
    final aspectRatio = maxWidth / maxHeight;

    double width = sourceWidth;
    double height = sourceHeight;
    final flip = width > height;

    if (flip) {
      final temp = width;
      width = height;
      height = temp;
    }

    if (height > 10000) {
      final multiple = (height / 2500).ceilToDouble();
      width /= multiple;
      height /= multiple;
    } else {
      final scale = width / height;
      if (scale > aspectRatio && scale <= 1) {
        if (height < 1664) {
          // Keep original size
        } else if (height < 4990) {
          width /= 2;
          height /= 2;
        } else if (height < 10240) {
          width /= 4;
          height /= 4;
        }
      } else if (scale > 0.5 && scale <= aspectRatio) {
        final multiple = (height / 1280).ceilToDouble();
        width /= multiple;
        height /= multiple;
      }
    }

    return flip ? Size(height, width) : Size(width, height);
  }

  Size _getThumbnailSize(double sourceWidth, double sourceHeight) {
    const longSide = MAX_SIZE_THUMBNAIL;
    const shortSide = MAX_SIZE_THUMBNAIL;

    double width = sourceWidth;
    double height = sourceHeight;
    final scale = width / height;

    if (width <= height) {
      if (scale > 0.5625) {
        width = min(width, shortSide.toDouble());
        height = width * sourceHeight / sourceWidth;
      } else {
        height = min(height, longSide.toDouble());
        width = height * sourceWidth / sourceHeight;
      }
    } else {
      if (scale > 0.5625) {
        height = min(height, shortSide.toDouble());
        width = height * sourceWidth / sourceHeight;
      } else {
        width = min(width, longSide.toDouble());
        height = width * sourceHeight / sourceWidth;
      }
    }

    return Size(width, height);
  }

  Size getTargetSize(
    double sourceWidth,
    double sourceHeight, {
    bool forThumbnail = false,
  }) =>
      forThumbnail
          ? _getThumbnailSize(sourceWidth, sourceHeight)
          : _getOriginalSize(sourceWidth, sourceHeight);

  double _calculateTargetKB(
    double srcW,
    double srcH,
    double dstW,
    double dstH,
    int originalKB,
  ) {
    const baseResolution = 2560.0;
    final srcArea = srcW * srcH;
    final dstArea = dstW * dstH;

    if (dstArea > 0 && srcArea > 0) {
      final ratio = dstArea / srcArea;
      return (originalKB * ratio * (pow(baseResolution, 2) / dstArea))
          .clamp(MIN_SIZE_KB.toDouble(), originalKB.toDouble());
    }
    return originalKB.toDouble();
  }

  int determineCompressionQuality(
    Size sourceSize,
    Size targetSize,
    int originalFileSizeKB,
  ) {
    if (targetSize == sourceSize) return defaultCompressQuality;
    final targetKB = _calculateTargetKB(
      sourceSize.width,
      sourceSize.height,
      targetSize.width,
      targetSize.height,
      originalFileSizeKB,
    );
    return (maxCompressQuality * (targetKB / originalFileSizeKB))
        .clamp(60, 95)
        .toInt();
  }

  Future<Uint8List?> _safeCompress({
    required String filePath,
    required Size targetSize,
    required int quality,
  }) async {
    try {
      final compressed = await ImageCompressor.compressFileAndGetBytes(
        filePath,
        quality: quality,
        minHeight: targetSize.height.toInt(),
        minWidth: targetSize.width.toInt(),
      );
      return compressed?.isNotEmpty ?? false ? compressed : null;
    } catch (e) {
      debugPrint('Compression Error: $e');
      return null;
    }
  }

  Future<File> compressAndGetFile({
    required String filePath,
    required Size targetSize,
    required int quality,
    required String outputPath,
    bool isThumbnail = false,
  }) =>
      _runWithLimit(() async {
        final compressed = await ImageCompressor.compressFileAndGetFile(
          filePath,
          outputPath,
          minWidth: targetSize.width.toInt(),
          minHeight: targetSize.height.toInt(),
          quality: quality,
          format: CompressFormat.jpeg,
        );
        return compressed != null ? File(compressed.path) : File(filePath);
      });

  Future<Uint8List> _resizeOriginalImage(
      String filePath, Size targetSize) async {
    final originalImage = await File(filePath).readAsBytes();
    final image = img.decodeImage(originalImage)!;
    final resized = img.copyResize(
      image,
      width: targetSize.width.toInt(),
      height: targetSize.height.toInt(),
    );
    return Uint8List.fromList(img.encodeJpg(resized));
  }

  void debugLog(
    Uint8List imageCompressData,
    double originalSizeKB,
    Size sourceSize,
    String filePath,
    Size targetSize,
    int quality,
  ) {
    if (!kDebugMode) return;

    final compressedSizeKB = imageCompressData.length / 1024;
    final reduction = originalSizeKB - compressedSizeKB;
    final reductionPercent = (reduction / originalSizeKB * 100);

    debugPrint('''
📊 Compression Report:
├── Original: ${sourceSize.width}x${sourceSize.height} (${originalSizeKB.toStringAsFixed(2)}KB)
├── Target: ${targetSize.width}x${targetSize.height} @ $quality%
├── Result: ${compressedSizeKB.toStringAsFixed(2)}KB
├── Reduced: ${reduction.toStringAsFixed(2)}KB (${reductionPercent.toStringAsFixed(1)}%)
└── Path: $filePath
''');
  }
}
